<?php
class OnlinePayment extends AppModel {
    var $name = 'OnlinePayment';
    var $displayField = 'id';
    var $actsAs = array('Containable');
    var $order = 'OnlinePayment.created ASC';

    // Define validation rules
    var $validate = array(
        'student_id' => array(
            'rule' => array('notEmpty'),
            'message' => 'Student ID is required'
        ),
        'amount' => array(
            'numeric' => array(
                'rule' => array('numeric'),
                'message' => 'Amount must be a valid number'
            ),
            'notEmpty' => array(
                'rule' => array('notEmpty'),
                'message' => 'Amount is required'
            )
        ),
        'payment_method' => array(
            'notEmpty' => array(
                'rule' => array('notEmpty'),
                'message' => 'Payment method is required'
            ),
            'inList' => array(
                'rule' => array('inList', array('GCASH', 'PMAYA', 'BANKT', 'ONSIT')),
                'message' => 'Payment method must be GCash, Pay Maya, Bank Transfer or On-site.'
            )
        ),
        'payment_status' => array(
            'notEmpty' => array(
                'rule' => array('notEmpty'),
                'message' => 'Payment status is required'
            ),
            'inList' => array(
                'rule' => array('inList', array('PENDNG', 'APPRVD','DCLNED','REVRSE')),
                'message' => 'Payment status must be Pending, Approved, Declined, Reversed'
            )
        )
    );

    // Define associations
    var $belongsTo = array(
        'Assessment' => array(
            'className' => 'Assessment',
            'foreignKey' => 'ref_no',
            'conditions' => '',
            'fields' => '',
            'order' => ''
        ),
        'Student' => array(
            'className' => 'Student',
            'foreignKey' => 'student_id',
            'conditions' => '',
            'fields' => '',
            'order' => ''
        )
    );

    var $hasMany = array(
        'Attachment'=> array(
            'className' => 'Attachment',
            'foreignKey' => 'ref_no',
            'conditions' => array('Attachment.ref_type'=>'PMT'),
            'fields' => array('ref_type','file_name'),
            'order' => ''
        )
    );

    /**
     * Process query conditions before find operations
     * Handles date filtering and search functionality
     *
     * @param array $queryData Query data array
     * @return array Modified query data
     */
    function beforeFind($queryData) {
        if($conds = $queryData['conditions']) {

            foreach($conds as $i => $cond) {
                if(!is_array($cond))
                    break;

                $keys = array_keys($cond);

                // Handle date range filtering - from date
                $search = 'OnlinePayment.from';
                if(in_array($search, $keys)) {
                    $from = $cond[$search];
                    unset($cond[$search]);
                    $cond['DATE(OnlinePayment.transaction_date) >='] = $from;
                }

                // Handle date range filtering - to date
                $search = 'OnlinePayment.to';
                if(in_array($search, $keys)) {
                    $to = $cond[$search];
                    unset($cond[$search]);
                    $cond['DATE(OnlinePayment.transaction_date) <='] = $to;
                }

                // Handle search by payment ID only
                $search = 'OnlinePayment.keyword';
                if(in_array($search, $keys)) {
                    $keyword = $cond[$search];
                    unset($cond[$search]);

                    // Search only by payment ID
                    $cond['OnlinePayment.id LIKE'] = '%' . $keyword . '%';
                }

                $conds[$i] = $cond;
            }

            $queryData['conditions'] = $conds;
        }

        return $queryData;
    }

    /**
     * Generate a unique payment reference number
     *
     * @return string Payment reference number
     */
    function generatePaymentReference() {
        $prefix = 'PAY';
        $timestamp = date('ymd');
        $random = strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 6));

        return $prefix . $timestamp . $random;
    }

    /**
     * Generate a unique ID with CJY prefix including school year and month
     * Format: CJY<Year><month code>0000<checksum> up to 9999<checksum>
     * The month code is the numeric equivalent of the month (1-9 for Jan-Sep, X for Oct, Y for Nov, Z for Dec)
     *
     * For example, for January 2025:
     * - First ID: CJY25100017 (where 7 is the checksum)
     * - Next IDs: CJY25100027, CJY25100037, etc.
     * - Up to: CJY25199997
     *
     * The function works by:
     * 1. Finding the latest ID with the given prefix
     * 2. Extracting the numeric part (4 digits before the checksum)
     * 3. Incrementing the numeric part by 1
     * 4. Applying the Luhn checksum algorithm to the new ID
     *
     * @param string $prefix Base prefix for the ID (default: CJY)
     * @param int $sy School year (e.g., 2025 for SY 2025-2026, default: current year)
     * @param int $month Month to use (1-12, default: current month)
     * @return string Generated ID (exactly 11 characters)
     */
    function generateID($prefix = 'CJY', $sy = null, $month = null) {
        // If no school year is provided, use the current year
        if ($sy === null) {
            $sy = date('Y');
        }

        // Get the last 2 digits of the school year
        $syCode = substr($sy, -2);

        // Get the month and convert to code
        // 1-9 for Jan-Sep, X for Oct, Y for Nov, Z for Dec
        if ($month === null) {
            $month = date('n');
        }

        $monthCode = '';
        if ($month >= 1 && $month <= 9) {
            $monthCode = $month-1;
        } else if ($month == 10) {
            $monthCode = 'X';
        } else if ($month == 11) {
            $monthCode = 'Y';
        } else if ($month == 12) {
            $monthCode = 'Z';
        }

        // Create the base prefix with school year and month
        $basePrefix = $prefix . $syCode . $monthCode;

        // Find the latest ID with this prefix
        $ID = 0;
        $PObj = $this->find('first', array(
            'recursive' => -1,
            'fields' => array('OnlinePayment.id'),
            'conditions' => array('OnlinePayment.id LIKE' => $basePrefix . '%'),
            'order' => array('OnlinePayment.id' => 'desc')
        ));

        if ($PObj) {
            // Extract the numeric part from the ID (excluding the checksum)
            $existingId = $PObj['OnlinePayment']['id'];
            $numericPart = substr($existingId, 6, 4); // Get the 4-digit numeric part
            $ID = (int)$numericPart;
        }

        // Increment the ID for the new record
        $ID++;

        // Create the new ID with the incremented numeric part
        $newId = $basePrefix . str_pad($ID, 4, '0', STR_PAD_LEFT);

        // Apply Luhn algorithm checksum (only once)
        $newId = $this->applyLuhnChecksum($newId);


        return $newId;
    }

    /**
     * Apply Luhn algorithm checksum to an ID
     *
     * @param string $id The ID to apply checksum to (should be 10 characters without checksum)
     * @return string The ID with the last digit replaced by the checksum (11 characters total)
     */
    public function applyLuhnChecksum($id) {
        // Make sure the input ID is exactly 10 characters (without checksum)
        if (strlen($id) != 10) {
            // Truncate or pad as needed
            if (strlen($id) > 10) {
                $id = substr($id, 0, 10);
            } else {
                $id = str_pad($id, 10, '0', STR_PAD_RIGHT);
            }
        }

        // Extract all numeric digits and convert letters to numeric values
        $numericPart = '';
        for ($i = 0; $i < strlen($id); $i++) {
            if (is_numeric($id[$i])) {
                $numericPart .= $id[$i];
            } else {
                // For month codes X, Y, Z, use 10, 11, 12 respectively
                $char = strtoupper($id[$i]);
                if ($char == 'X') {
                    $numericPart .= '10';
                } else if ($char == 'Y') {
                    $numericPart .= '11';
                } else if ($char == 'Z') {
                    $numericPart .= '12';
                } else if ($char >= 'A' && $char <= 'W') {
                    // For other letters A-W, use their position in alphabet (1-23)
                    $value = ord($char) - ord('A') + 1;
                    $numericPart .= $value;
                } else {
                    // For other non-numeric characters, use their ASCII value modulo 10
                    $asciiValue = ord($id[$i]) % 10;
                    $numericPart .= $asciiValue;
                }
            }
        }

        // Calculate the Luhn checksum
        $checksum = $this->calculateLuhnChecksum($numericPart);

        // Add the checksum to the ID
        $result = $id . $checksum;

        return $result;
    }

    /**
     * Calculate Luhn algorithm checksum
     *
     * @param string $number The number to calculate checksum for
     * @return int The checksum digit (0-9)
     */
    private function calculateLuhnChecksum($number) {
        // Luhn algorithm implementation
        $sum = 0;
        $alt = false;

        // Start from the right
        for ($i = strlen($number) - 1; $i >= 0; $i--) {
            $n = intval($number[$i]);

            if ($alt) {
                $n *= 2;
                if ($n > 9) {
                    $n -= 9;
                }
            }

            $sum += $n;
            $alt = !$alt;
        }

        // The check digit is the amount needed to make the sum
        // a multiple of 10
        return (10 - ($sum % 10)) % 10;
    }

    /**
     * Validate an ID using the Luhn checksum
     *
     * @param string $id The ID to validate (must be exactly 11 characters)
     * @return boolean True if valid, false otherwise
     */
    public function validateID($id) {
        // Check if the ID is exactly 11 characters
        if (strlen($id) != 11) {
            return false;
        }

        // Extract the provided checksum (last digit)
        $providedChecksum = intval(substr($id, 10, 1));

        // Get the base ID (without checksum)
        $baseId = substr($id, 0, 10);

        // Calculate what the checksum should be
        $freshId = $this->applyLuhnChecksum($baseId);
        $calculatedChecksum = intval(substr($freshId, 10, 1));

        // Compare the checksums
        return $providedChecksum === $calculatedChecksum;
    }

    /**
     * Get payment by reference number
     *
     * @param string $refNo Payment reference number
     * @return array Payment data or false if not found
     */
    function getByReference($refNo) {
        return $this->find('first', array(
            'conditions' => array('OnlinePayment.payment_reference' => $refNo),
            'contain' => array('Assessment', 'Student')
        ));
    }

    /**
     * Get payments by assessment ID
     *
     * @param string $assessmentId Assessment ID
     * @return array List of payments
     */
    function getByAssessment($assessmentId) {
        return $this->find('all', array(
            'conditions' => array('OnlinePayment.ref_no' => $assessmentId),
            'contain' => array('Student'),
            'order' => array('OnlinePayment.created DESC')
        ));
    }

    /**
     * Get payments by student ID
     *
     * @param string $studentId Student ID
     * @return array List of payments
     */
    function getByStudent($studentId) {
        return $this->find('all', array(
            'conditions' => array('OnlinePayment.student_id' => $studentId),
            'contain' => array('Assessment'),
            'order' => array('OnlinePayment.created DESC')
        ));
    }

    /**
     * Update payment status
     *
     * @param string $id Payment ID
     * @param string $status New status (Pending, Completed, Failed, Cancelled)
     * @param string $notes Optional notes about the status change
     * @return boolean Success or failure
     */
    function updateStatus($id, $status, $notes = null) {
        $data = array(
            'id' => $id,
            'payment_status' => $status
        );

        if ($status == 'Completed') {
            $data['confirmation_date'] = date('Y-m-d H:i:s');
        }

        if ($notes !== null) {
            $data['notes'] = $notes;
        }

        $this->id = $id;
        return $this->save($data, false);
    }

}
?>
