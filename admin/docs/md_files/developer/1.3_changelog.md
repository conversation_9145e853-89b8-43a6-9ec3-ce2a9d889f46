# 1.3 Changelog

This document tracks significant changes to the LAPIS system, organized by version number.

## v1.1.36 (Current)

### Enhancements
- Updated textbook and school supplies amounts across all grade levels in `admin/config/policy/textbook_supplies.js`
- Implemented comprehensive online payments system with PayMaya integration
- Added online payments monitoring module for administrators (`admin/views/students/payments.php`)
- Enhanced payment filtering and search capabilities in `admin/controllers/online_payments_controller.js`
- Improved payment proof upload functionality with better error handling
- Added payment status management and tracking features
- Implemented date range filtering for payment transactions
- Added search functionality for payment records by student name, reference number, and payment reference

### Technical Improvements
- Enhanced API endpoints for online payments management
- Improved payment data formatting and display
- Added comprehensive payment status tracking (Pending, Approved, Declined, Reversed)
- Implemented payment method categorization (GCash, PayMaya, Bank Transfer, On-site)
- Enhanced payment record pagination and filtering

### Documentation Updates
- Updated user documentation for textbook and school supplies integration
- Added comprehensive documentation for online payments monitoring module
- Updated change log with current version information

## v1.1.35

### Enhancements
- Added option "Skip Required Payments" in registration module
- Added validation and warning messages with this payment option
- Updated Admin modules for validating registration with this payment option

## v1.1.32

### Enhancements
- Added Waived Registration Fee discount feature in the Adjustments tab
- Added Total Discounts line in the Assessment Request Form PDF at the same Y position as UPON ENROLLMENT
- Updated discount policy text for Waived Registration Fee to be more descriptive

## v1.1.31

### Enhancements
- Improved discount distribution system to properly handle cash payment plans
- Updated discount application logic to apply lastDueDiscount to the first payment for cash plans
- Modified payment schedule calculation to only distribute discounts for non-cash plans
- Enhanced discount display in tuition data to show discounts differently based on payment plan
- Updated documentation to reflect changes in discount distribution system

### Bug Fixes
- Fixed issue where payment plan would revert to cash when processing assessment requests with non-cash plans
- Fixed year_level_id watcher to preserve existing payment plan when year level changes

## v1.1.29

### Bug Fixes
- Fixed textbook option handling in assessment controller to properly handle undefined/null values
- Improved code formatting and indentation in assessment controller
- Fixed conditional check for textbook and supplies in assessment calculator
- Removed debug console.log statement from assessment controller
- Updated label text in assess.php from "Textbook and School Supplies" to "Textbooks and School Supplies"
- Updated label text in assess.php from "Textbook Amount" to "Textbooks Amount"
- Fixed label in assessment calculator from "Tuition Fee (from ACalc)" to "Tuition Fee"

## v1.1.28 and earlier

For changes in previous versions, please refer to the Git commit history.
