"use strict";
define(['app','exceljs','atomic/bomb','filesaver','api',
    'students/registrations_controller',
    'students/validations_controller',
    'students/confirmations_controller',
    'students/updates_controller',
    'students/assessments_controller',
    ], function (app,ExcelJS) {
    app.register.controller('StudentMainController',['$scope','$rootScope','$filter','$timeout','api','Atomic','aModal',function ($scope,$rootScope,$filter,$timeout,api,atomic,aModal) {
       const $selfScope =  $scope;
       const STUDENT_INFO = 0;
       const HOUSEHOLD_INFO = 1;
       const SCHOLASTIC_INFO = 2;
       const PAYMENT_INFO = 3;
       const VALIDATION_INFO = 4;
       $scope = this;
       atomic.ready(function(response){
           $scope.Departments= atomic.Departments;
           let deptOrder = ['PS', 'GS', 'MS', 'HS', 'SH'];
           
           // Filter out CLB and sort by department order and year level order
           $scope.YearLevels = $filter('filter')(atomic.YearLevels, function(yl) {
               return yl.id !== 'CL';
           });
           
           // Sort YearLevels by department order and year level order
           $scope.YearLevels.sort(function(a, b) {
               let deptIndexA = deptOrder.indexOf(a.department_id);
               let deptIndexB = deptOrder.indexOf(b.department_id);
               if (deptIndexA !== deptIndexB) {
                   return deptIndexA - deptIndexB;
               }
               return a.order - b.order;
           });
        });
       $rootScope.$watch('_APP',function(app){
            if(!app) return;
         });
       $scope.init = function (module_name) { 
         $scope.ActiveModule = module_name;
         initApp();
         $timeout(loadStudents,1500);
         $scope.StuHeaders = ['Date Submitted','Ref No.','Student Name','Incoming Grade Level','Student Type','Amount Paid','Status'];
         $scope.StuProps = ['submitted_date','id','full_name','incoming_level','student_type','paid_amount','status'];
         $scope.AppStatuses = [
            {id:'PENDING',name:'Pending'},
            {id:'VALIDATED',name:'Validated'},
            {id:'APPROVED',name:'Approved'},
            {id:'DECLINED',name:'Declined'},
            {id:'ALL',name:'All'},
         ];
         
         $scope.StudentTypes = [
                {id:'NEW', name:'New Student'},
                {id:'OLD', name:'Current Student'},
            ];          
            $scope.GenderOptions =[
                    {id:'M', name:'He is an incoming '},
                    {id:'F', name:'She is an incoming '},
                ];
            $scope.SubsidyOptions=[
                {id:'ESC',name:'ESC Grantee'},
                {id:'QVR',name:'QVR Grantee'},
                {id:'PUB',name:'Public School Grantee'},
                {id:'NOS',name:'NO School Subsidy'}
            ];
            $scope.SHTrackOptions = [
                {id:'SHABM', name:'ABM'},
                {id:'SHSTE', name:'STEM'},
            ];
            $scope.Genders = [
                {id:'M',name:'Male'},
                {id:'F',name:'Female'}
            ];
            $scope.SchoolTypes = [
                {id:'PRV',name:'Private'},
                {id:'PUB',name:'Public'}
            ];

            $scope.PayModeOptions = [
                {id:'GCASH',name:'GCash'},
                {id:'PMAYA',name:'Pay Maya'},
                {id:'BANKT',name:'Bank Transfer'},
                {id:'ONSIT',name:'On-Site Payment'},
                {id:'NOPAY',name:'Skipped Required Payment'},
                {id:'NONE',name:'No Payment'}

            ];
            $scope.YearLevels =[
                    {id:'JN', name:'Junior Nursery', dept_id:'PS'},
                    {id:'SN', name:'Senior Nursery', dept_id:'PS'},
                    {id:'KN', name:'Kindergarten', dept_id:'PS'},
                    {id:'G1', name:'Grade 1', dept_id:'GS'},
                    {id:'G2', name:'Grade 2', dept_id:'GS'},
                    {id:'G3', name:'Grade 3', dept_id:'GS'},
                    {id:'G4', name:'Grade 4', dept_id:'GS'},
                    {id:'G5', name:'Grade 5', dept_id:'MS'},
                    {id:'G6', name:'Grade 6', dept_id:'MS'},
                    {id:'G7', name:'Grade 7', dept_id:'MS'},
                    {id:'G8', name:'Grade 8', dept_id:'MS'},
                    {id:'G9', name:'Grade 9', dept_id:'HS'},
                    {id:'GX', name:'Grade 10', dept_id:'HS'},
                    {id:'GY', name:'Grade 11', dept_id:'HS'},
                    {id:'GZ', name:'Grade 12', dept_id:'HS'},
                ];
            $selfScope.$watchGroup(['SMC.Child.department','SMC.Scholastic.incoming_level'],function(vals){
                let dept = vals[0];
                let level = vals[1];
                $scope.LevelOptions=[];  
                if(dept)
                    $scope.LevelOptions = $filter('filter')($scope.YearLevels,{dept_id:dept});
                if(level)
                    $scope.LevelOptions = $filter('filter')($scope.YearLevels,{id:level});
            });
            $selfScope.$watchGroup(['SMC.PaymentMode','SMC.PaidAmount','SMC.AppNotes'],function(values){
                let isAppValid = !!values[0] && !!values[1] && !!values[2];
                $scope.AppValid = isAppValid;
            });

            $selfScope.$watch('SMC.Guardian',function(guardian){
                $scope.hasValidEmail = false;
                if(guardian!= undefined){

                    $scope.hasValidEmail = !!guardian.father_email || !!guardian.mother_email || !!guardian.guardian_email;
                    console.log($scope.hasValidEmail,guardian);
                }
            });
            
            $selfScope.$watch('SMC.DateFilterFrom',function(dateFrom){
                $scope.DateFilterTo =  dateFrom;
            }); 

            $selfScope.$on('AppUpdated',function(args,data){
                loadStudents($scope.CurrentPage);
                if(data.meta.code==200){
                    
                    alert(data.meta.message);
                    
                    $timeout(function(){
                        if(data.action=='approve')
                            $scope.AppStatus ='APPROVED';
                        if(data.action=='decline')
                            $scope.AppStatus ='DECLINED';
                        $scope.AppSaving = false;    
                        $selfScope.$emit('RequestInquiryInfo',$scope.AppNo);    
                    },1500);
                    
                }
           });

           $selfScope.$on('RequestInquiryInfo',function(args,appNo){
                let filter = {id:appNo};
                let success = function(response){
                    let item = response.data[0];
                    $scope.ActiveRecord = item;
                    displayDetails(item);
                }
                let error = function(response){
                    alert(response.message);
                }
                api.GET('inquiries',filter,success,error);
           });


           $selfScope.$on('loadStudents',function(args){
            loadStudents($scope.CurrentPage);
           });

           $selfScope.$watch('SMC.ActiveModule',function(module_name){
            $scope.AllowRecordEdit = module_name =='registration';
           });
       }
        let initApp = function(){
            $scope.Child = {};
            $scope.Scholastic = {};
            $scope.Guardian = {};
            $scope.PaymentMode =null;
            $scope.ProofOfPayment = null;
            $scope.LevelOptions=[];
            $scope.Child.sched_ampm = null;
            $scope.Child.sh_track= null;
            $scope.AppNo =null;
            $scope.ActiveRecord = null;
            $scope.Validation = {};
            $scope.Confirmation = {};
            $scope.CurrentPage = 1;
            $scope.hasValidEmail = false;
            if(!$scope.DisplayFilter){
                $scope.DisplayFilter= 'PENDING';
                if($scope.ActiveModule=='confirmation')
                    $scope.DisplayFilter= 'VALIDATED';
            }

            $scope.DateFilterFrom = null;
            $scope.DateFilterTo = null;
            atomic.fuse();
            
        }
        let loadStudents = function (page, from, to,search){
            atomic.fuse();
            $scope.Students = [];
            let filter ={page:page};
            if($scope.DisplayFilter!='ALL')
            filter.status = $scope.DisplayFilter;

            if(from && to){
                filter.from = $filter('date')(from, 'yyyy-MM-d');
                filter.to = $filter('date')(to, 'yyyy-MM-d');
            }
            if(search){
                delete filter.from;
                delete filter.to;
                filter.keyword = search.keyword;
                filter.fields = search.fields;
            }
            $scope.CurrentPage = page;

            $scope.isLoading = true;
            let success = function(response){
                $scope.isLoading = false;
                let students = response.data;
                for(var i in students){
                    let student = students[i];
                    let status =  student.status;
                    let submitDate = student.submitted_date;
                    
                    switch(status){
                        case 'APPROVED':
                            students[i].class = 'success';
                        break;
                        case 'DECLINED':
                            students[i].class = 'danger';
                        break;
                        case 'VALIDATED':
                            students[i].class = 'info';
                        break;
                    }
                    student.submitted_date = $filter('date')(new Date(submitDate), 'MMM d, yyyy');
                    let appObj = JSON.parse(student.application_details);
                    student.application_details = appObj;
                    let inLevel = $filter('filter')($scope.YearLevels,{id:appObj.scholastic.incoming_level});
                    if(inLevel){
                        if(inLevel.length)
                            inLevel = inLevel[0];
                        student.incoming_level = inLevel.description;
                    }
                    students[i] = student;
                }
                $scope.Students = students;
                $scope.Meta = response.meta;
            };
            let error = function(response){
                $scope.isLoading = false;
                $scope.Meta = {page:0,pages:0,limit:0};
                $scope.CurrentPage = 0;
            };
            api.GET('inquiries',filter,success,error);
        }
       let displayDetails = function (item){
            $scope.AppNo = item.id;
            $scope.AppStatus = item.status;
            $scope.AppNotes = null;
            $scope.PaidAmount = null;
            if(item.status!='PENDING'){
                $scope.AppNotes = item.notes;
                $scope.PaidAmount = item.paid_amount;
            }
            $scope.AppValid=false;
            let appObj = item.application_details;
            
            appObj.student.birthday  = new Date(appObj.student.birthday);
            $scope.Child = appObj.student;

            $scope.Household = appObj.household;
            $scope.Guardian = appObj.guardian;
            $scope.Scholastic = appObj.scholastic;
            $scope.PaymentMode = appObj.payment_mode;
            $scope.Validation = appObj.validations;
            $scope.Confirmation = appObj.confirmations;
            $scope.AppSubmittedDate = $filter('date')(new Date(item.submitted_date), 'EEE MMM d yyyy h:mm a');
            if(item.updated_date)
                $scope.AppUpdatedDate = $filter('date')(new Date(item.updated_date), 'EEE MMM d yyyy h:mm a');
            $scope.AttachmentURL = '../api/attachments/view/'+item.attachment;
       }
        $scope.gotoPage =function(page){
            let from = $scope.DateFilterFrom;
            let to = $scope.DateFilterTo;
            loadStudents(page, from, to);
            
        }
       $scope.setPage = function(page){
            $scope.ActiveTabIndex = page-1;
        }
        $scope.filterRecords = function(){
            $scope.isRecordsFiltered = true;
            let from = $scope.DateFilterFrom;
            let to = $scope.DateFilterTo;
            let page = 1;
            loadStudents(page, from, to);
        }
        $scope.toggleSeach = function(){
            $scope.showSearch = !$scope.showSearch;

        }
        $scope.searchRecords = function(){
            $scope.isRecordsFiltered = true;
            let keyword = $scope.StudentSearch;
            let fields = ['id','first_name','last_name','middle_name'];
            let search = {keyword:keyword,fields:fields};
            let page = 1;
            loadStudents(page, null, null, search);

        }
        $scope.clearFilter = function(){
            $scope.isRecordsFiltered = false;
            $scope.DateFilterTo = null;
            $scope.DateFilterFrom = null;
            $scope.StudentSearch = null;
            loadStudents(1);

        };
        $scope.exportToExcel = function(){

            $scope.ExportingFile = true;
            let filter ={limit:'less'};
            if($scope.DisplayFilter!='ALL')
                filter.status = $scope.DisplayFilter;
            if($scope.StudentSearch && $scope.showSearch){
                filter.keyword = $scope.StudentSearch;
                filter.fields = ['id','first_name','last_name','middle_name'];
            }
            let success = function(response){
                let students = response.data;
                for(var i in students){
                    let student = students[i];
                    let submitDate = students[i].submitted_date;
                    students[i].submitted_date = $filter('date')(new Date(submitDate), 'MMM d, yyyy');
                    let appObj = JSON.parse(student.application_details);
                    let levelId = appObj.scholastic.incoming_level;
                    console.log($scope.YearLevels,levelId);
                    let inLevel = $filter('filter')($scope.YearLevels,{id:levelId})[0];
                    if(inLevel)
                        student.incoming_level = inLevel.name;
                    students[i]=student;
                }
                prepareFile(students);

            };
            let error = function(response){
                alert(response.message);
                $scope.ExportingFile  =false;
            };
            api.GET('inquiries',filter,success,error);
            
            function prepareFile(students){
                let timeStamp = $filter('date')(new Date(), 'yyyy_MMM_d_hh_mm_a');

                if($scope.isRecordsFiltered){
                    let from = $filter('date')($scope.DateFilterFrom, 'yyyy_MMM_d_');
                    let to = $filter('date')($scope.DateFilterTo, 'yyyy_MMM_d_');
                    timeStamp =`from_${from}to_${to}`;
                }
                if($scope.StudentSearch && $scope.showSearch){
                    timeStamp = $filter('date')(new Date(), 'yyyy_MMM_d_hh_mm_a');
                    let search = $scope.StudentSearch.toUpperCase();
                    timeStamp = `search_for_${search}_${timeStamp}`;
                }

                let status = $scope.DisplayFilter;
                let fileName = `Online Registrations_${status}_${timeStamp}.xlsx`;
                let sheetName = 'Online Registrations';
                const workbook = new ExcelJS.Workbook();
                const worksheet = workbook.addWorksheet(sheetName);
                // Prepare columns
                var columns  = [];
                for(var i in $scope.StuHeaders){
                    let header = $scope.StuHeaders[i];
                    let colObj = {header:header};
                    colObj.width=i==2?40:12;
                    columns.push(colObj);
                }
                var excelHeaders = ['Schedule','Bank Name','Purpose', 'Validation','Payment Ref No','OR Number']
                var excelProps = ['schedule','bank_name','purpose','validation','payment_ref_no','or_number'];
               

                for(var i in excelHeaders){
                    let header = excelHeaders[i];
                    let colObj = {header:header};
                    colObj.width=12;
                    columns.push(colObj);
                }

                worksheet.columns = columns;
                // Prepare rows
                var rows =[];
                for(var j in students){
                    let studObj = students[j];
                    var row =[];
                    for(var k in $scope.StuProps){
                        let field = $scope.StuProps[k];
                        let value = studObj[field];
                        row.push(value);
                    }

                    for(var l in excelProps){
                        let appObj = JSON.parse(studObj.application_details);
                        let field = excelProps[l];
                        let value ='';
                        if( field=='or_number'){
                            if(appObj.confirmations)
                                value = appObj.confirmations.or_number;
                        }else if(field=='schedule'){
                            value = appObj.student.sched_ampm;
                        }else if(appObj.validations){
                            value = appObj.validations[field];
                        }
                        row.push(value);
                    }
                    rows.push(row);
                }


                worksheet.addRows(rows);
                $timeout(function(){
                    $scope.ExportingFile =false;
                    workbook.xlsx.writeBuffer().then((data) => {
                        const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8' });
                        // From FileSaver.js
                        saveAs(blob, fileName);
                    }); 
                },500);
            }
        }

       $scope.openModal = function(item){
            displayDetails(item);
            $scope.ActiveTabIndex = STUDENT_INFO;
            switch($scope.ActiveModule){
                case 'confirmation':
                    $scope.ActiveTabIndex = PAYMENT_INFO;
                break;
                case 'validation':
                    $scope.ActiveTabIndex = VALIDATION_INFO;
                break;
            }
            aModal.open('ActiveRecordModal');
       }
       $scope.closeModal = function() {
            initApp();
            aModal.close('ActiveRecordModal');
       }

       /**
        * Send registration confirmation email to guardians with notify flag set to 'Y'
        * This function calls the API endpoint that sends confirmation emails
        */
       $scope.sendConfirmation = function() {
            if (!$scope.AppNo) {
                alert('No registration reference number found.');
                return;
            }

            // Show sending indicator
            $scope.isSendingConfirmation = true;

            let success = function(response) {
                $scope.isSendingConfirmation = false;
                let isSent =  response.meta.code==200;
                if(isSent){
                    alert('Registration confirmation email sent successfully to  the recipient(s).');
                } else {
                    alert('No email addresses found for this registration. Please update the guardian information with valid email addresses.');
                }
            };

            let error = function(response) {
                $scope.isSendingConfirmation = false;
                alert( (response.message || 'Unknown error'));
            };

            // Call the API endpoint to send the confirmation
            // Use POST instead of GET to ensure it works with authentication
            let sendInfo = {id:$scope.AppNo};
            api.POST('inquiries/admin/send_confirmation',sendInfo,success,error);
       }
    }]);
});
